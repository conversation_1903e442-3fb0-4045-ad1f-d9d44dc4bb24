'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, User, Calendar, MessageSquare, Send, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import UserProfileCard from '@/components/forum/UserProfileCard';
import ReactionButton from '@/components/forum/ReactionButton';
import ForumPost from '@/components/forum/ForumPost';
import ForumReply from '@/components/forum/ForumReply';
import { useCSRF } from '@/hooks/useCSRF';
import { FormLoadingState, PageErrorState } from '@/components/ui/page-loading-states';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
  category?: string;
  tags?: string[];
  viewCount?: number;
  author: {
    id: string;
    email: string;
    name?: string;
    profile?: {
      firstName?: string;
      lastName?: string;
      jobTitle?: string;
      company?: string;
      location?: string;
      profilePictureUrl?: string;
      experienceLevel?: string;
      bio?: string;
      profileCompletionScore?: number;
    };
    _count?: {
      forumPosts?: number;
      forumReplies?: number;
      achievements?: number;
    };
  };
  replies: Array<{
    id: string;
    content: string;
    createdAt: string;
    author: {
      id: string;
      email: string;
      name?: string;
      profile?: {
        firstName?: string;
        lastName?: string;
        jobTitle?: string;
        company?: string;
        profilePictureUrl?: string;
        experienceLevel?: string;
      };
    };
  }>;
}

interface ReplyForm {
  content: string;
}

export default function ForumPostPage({ params }: { params: Promise<{ postId: string }> }) {
  const { status } = useSession();
  const router = useRouter();
  const [post, setPost] = useState<ForumPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const { getHeaders, isLoading: csrfLoading } = useCSRF();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ReplyForm>();

  const contentValue = watch('content', '');

  const fetchPost = useCallback(async () => {
    try {
      setLoading(true);
      const resolvedParams = await params;

      // Fetch individual post instead of all posts (fixes N+1 query)
      const response = await fetch(`/api/forum/posts/${resolvedParams.postId}`);

      if (!response.ok) {
        if (response.status === 404) {
          setError('Post not found');
          return;
        }
        throw new Error('Failed to fetch post');
      }

      const result = await response.json();

      if (!result.success || !result.data) {
        setError('Post not found');
        return;
      }

      setPost(result.data);
    } catch (err) {
      console.error('Error fetching post:', err);
      setError('Failed to load post');
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchPost();
    }
  }, [status, fetchPost, router]);

  const onSubmitReply = async (data: ReplyForm) => {
    if (status !== 'authenticated') {
      setError('You must be logged in to reply');
      return;
    }

    setIsSubmittingReply(true);
    setError(null);

    try {
      const resolvedParams = await params;
      const response = await fetch(`/api/forum/posts/${resolvedParams.postId}/replies`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          content: data.content.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create reply');
      }

      const newReply = await response.json();
      
      // Add the new reply to the post
      if (post) {
        setPost({
          ...post,
          replies: [...post.replies, newReply],
        });
      }
      
      reset(); // Clear the form
    } catch (err) {
      console.error('Error creating reply:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsSubmittingReply(false);
    }
  };

  // Post edit/delete handlers
  const handleEditPost = (postToEdit: any) => {
    // Navigate to edit page or show edit modal
    router.push(`/forum/posts/${postToEdit.id}/edit`);
  };

  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this post?')) return;

    try {
      const response = await fetch(`/api/forum/posts/${postId}`, {
        method: 'DELETE',
        headers: getHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete post');
      }

      // Navigate back to forum after successful deletion
      router.push('/forum');
    } catch (err) {
      console.error('Error deleting post:', err);
      setError(err instanceof Error ? err.message : String(err));
    }
  };

  // Reply edit/delete handlers
  const handleEditReply = (replyToEdit: any) => {
    // For now, just log - we can implement inline editing later
    console.log('Edit reply:', replyToEdit);
    // TODO: Implement inline reply editing
  };

  const handleDeleteReply = async (replyId: string) => {
    if (!confirm('Are you sure you want to delete this reply?')) return;

    try {
      const response = await fetch(`/api/forum/replies/${replyId}`, {
        method: 'DELETE',
        headers: getHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete reply');
      }

      // Remove the reply from the post
      if (post) {
        setPost({
          ...post,
          replies: post.replies.filter(reply => reply.id !== replyId),
        });
      }
    } catch (err) {
      console.error('Error deleting reply:', err);
      setError(err instanceof Error ? err.message : String(err));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDisplayName = (author: { name?: string; email: string }) => {
    return author.name || author.email.split('@')[0];
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <FormLoadingState message="Loading forum post..." />
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to view this post.</p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error || 'Post not found'}</p>
          <Link href="/forum" className="text-blue-600 hover:underline mt-4 inline-block">
            Back to Forum
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <Link
          href="/forum"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Forum
        </Link>
      </div>

      {/* Main Post */}
      <ForumPost
        post={post}
        onEdit={handleEditPost}
        onDelete={handleDeletePost}
        showFullContent={true}
        className="mb-8"
      />

      {/* Replies Section */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Replies ({post.replies.length})
        </h2>

        {post.replies.map((reply) => (
          <ForumReply
            key={reply.id}
            reply={reply}
            onEdit={handleEditReply}
            onDelete={handleDeleteReply}
            className="ml-8"
          />
        ))}

        {/* Reply Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Add a Reply
          </h3>

          {error && (
            <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmitReply)} className="space-y-4">
            <div>
              <textarea
                id="content"
                rows={4}
                {...register('content', {
                  required: 'Reply content is required',
                  maxLength: {
                    value: 2000,
                    message: 'Reply must be 2000 characters or less',
                  },
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Write your reply..."
                disabled={isSubmittingReply}
              />
              <div className="flex justify-between mt-1">
                {errors.content && (
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.content.message}</p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 ml-auto">
                  {contentValue.length}/2000 characters
                </p>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isSubmittingReply || csrfLoading}
              className="flex items-center gap-2"
            >
              {isSubmittingReply ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Posting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Post Reply
                </>
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
